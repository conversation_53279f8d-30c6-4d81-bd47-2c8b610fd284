import { useState, useCallback } from "react";
import { useProductCategories } from "@/data/useProductCategories";
import { useProducts } from "@/data/useProducts";
import { Card } from "primereact/card";
import { Button } from "primereact/button";
import { Toolbar } from "primereact/toolbar";
import { InputText } from "primereact/inputtext";
import { Dropdown } from "primereact/dropdown";
import type { ColDef, ICellRendererParams } from 'ag-grid-community';
import { AgGridReact } from "ag-grid-react";
import type { Product, ProductCategory } from "@kassierer/shared/model";
import { PriceCentUtils } from "@/utils/priceCents";
import { NavLink } from "react-router-dom";

import { ModuleRegistry, AllCommunityModule } from 'ag-grid-community';
    
    ModuleRegistry.registerModules([ AllCommunityModule ]);

// Temporary product type for local state
type TempProduct = {
  id: string;
  name: string;
  priceCents: number | null;
  categoryId: string | null;
  isTemporary: true;
};

// Combined type for table data
type TableProduct = Product | TempProduct;

// Update type for temporary products
type TempProductUpdate = Partial<Omit<TempProduct, 'id' | 'isTemporary'>>;

// Status cell renderer component
const StatusCellRenderer = ({ data }: { data: TableProduct }) => {
  const isTemporary = 'isTemporary' in data && data.isTemporary;
  return (
    <span className={isTemporary ? "text-orange-600 font-semibold" : "text-green-600 font-semibold"}>
      {isTemporary ? "Neu" : "OK"}
    </span>
  );
};

// Editable name cell renderer component
const EditableNameCellRenderer = ({
  data,
  onUpdate
}: {
  data: TableProduct;
  onUpdate: (id: string, updates: TempProductUpdate) => void;
}) => {
  const isTemporary = 'isTemporary' in data && data.isTemporary;

  if (isTemporary) {
    return (
      <InputText
        value={data.name}
        onChange={(e) => onUpdate(data.id, { name: e.target.value })}
        placeholder="Produktname eingeben"
        className="w-full"
      />
    );
  }

  return <span>{data.name}</span>;
};

// Editable price cell renderer component
const EditablePriceCellRenderer = ({
  data,
  onUpdate
}: {
  data: TableProduct;
  onUpdate: (id: string, updates: TempProductUpdate) => void;
}) => {
  const isTemporary = 'isTemporary' in data && data.isTemporary;

  if (isTemporary) {
    return (
      <InputText
        value={data.priceCents?.toString() || ''}
        onChange={(e) => {
          const value = e.target.value;
          const priceCents = value === '' ? null : parseInt(value, 10);
          onUpdate(data.id, { priceCents: isNaN(priceCents!) ? null : priceCents });
        }}
        placeholder="Preis in Cent"
        className="w-full"
        keyfilter="int"
      />
    );
  }

  return <span>{data.priceCents !== null ? PriceCentUtils.toMoneyString(data.priceCents) : '-'}</span>;
};

// Editable category cell renderer component
const EditableCategoryCellRenderer = ({
  data,
  onUpdate,
  productCategories
}: {
  data: TableProduct;
  onUpdate: (id: string, updates: TempProductUpdate) => void;
  productCategories: ProductCategory[];
}) => {
  const isTemporary = 'isTemporary' in data && data.isTemporary;

  if (isTemporary) {
    const categoryOptions = productCategories.map(cat => ({
      label: cat.name,
      value: cat.id
    }));

    return (
      <Dropdown
        value={data.categoryId}
        options={categoryOptions}
        onChange={(e) => onUpdate(data.id, { categoryId: e.value })}
        placeholder="Kategorie wählen"
        className="w-full"
      />
    );
  }

  const category = productCategories.find(cat => cat.id === data.categoryId);
  if (category) {
    return (
      <NavLink
        to={`/admin/product-categories/${category.id}`}
        className="text-primary no-underline hover:underline"
      >
        {category.name}
      </NavLink>
    );
  }
  return <span>-</span>;
};

// Action cell renderer component
const ActionCellRenderer = ({
  data,
  onDelete,
  onDeleteTemp
}: {
  data: TableProduct;
  onDelete: (product: Product) => void;
  onDeleteTemp: (id: string) => void;
}) => {
  const isTemporary = 'isTemporary' in data && data.isTemporary;

  return (
    <div className="flex gap-2">
      <Button
        label="Löschen"
        icon="pi pi-trash"
        severity="danger"
        size="small"
        onClick={() => {
          if (isTemporary) {
            onDeleteTemp(data.id);
          } else {
            onDelete(data as Product);
          }
        }}
      />
    </div>
  );
};


function AdminProductsPage() {
  const products = useProducts((state) => state.products);
  const deleteProduct = useProducts((state) => state.deleteProduct);
  const createProduct = useProducts((state) => state.createProduct);
  const categories = useProductCategories((state) => state.categories);

  // Local state for temporary products
  const [tempProducts, setTempProducts] = useState<TempProduct[]>([]);

  // Combine real and temporary products for display
  const allProducts: TableProduct[] = [...tempProducts, ...products];

  // Helper functions
  const addTempProduct = useCallback(() => {
    const newTempProduct: TempProduct = {
      id: crypto.randomUUID(),
      name: '',
      priceCents: null,
      categoryId: null,
      isTemporary: true,
    };
    setTempProducts(prev => [newTempProduct, ...prev]);
  }, []);

  const updateTempProduct = useCallback((id: string, updates: TempProductUpdate) => {
    setTempProducts(prev => prev.map(product =>
      product.id === id ? { ...product, ...updates } : product
    ));
  }, []);

  const saveTempProduct = useCallback((tempProduct: TempProduct) => {
    if (tempProduct.name && tempProduct.priceCents !== null && tempProduct.categoryId) {
      createProduct({
        name: tempProduct.name,
        priceCents: tempProduct.priceCents,
        categoryId: tempProduct.categoryId,
      });
      setTempProducts(prev => prev.filter(p => p.id !== tempProduct.id));
    }
  }, [createProduct]);

  const deleteTempProduct = useCallback((id: string) => {
    setTempProducts(prev => prev.filter(p => p.id !== id));
  }, []);

  const columnDefs: ColDef<TableProduct>[] = [
    {
      headerName: 'Status',
      field: 'isTemporary',
      width: 100,
      cellRenderer: (params: ICellRendererParams<TableProduct>) => (
        <StatusCellRenderer data={params.data!} />
      ),
      sortable: true,
      filter: true
    },
    {
      headerName: 'Name',
      field: 'name',
      flex: 1,
      cellRenderer: (params: ICellRendererParams<TableProduct>) => (
        <EditableNameCellRenderer
          data={params.data!}
          onUpdate={(id, updates) => {
            updateTempProduct(id, updates);
            // Check if product should be saved
            const updatedProduct = tempProducts.find(p => p.id === id);
            if (updatedProduct) {
              const finalProduct = { ...updatedProduct, ...updates };
              if (finalProduct.name && finalProduct.priceCents !== null && finalProduct.categoryId) {
                saveTempProduct(finalProduct);
              }
            }
          }}
        />
      ),
      sortable: true,
      filter: true
    },
    {
      headerName: 'Preis',
      field: 'priceCents',
      width: 150,
      cellRenderer: (params: ICellRendererParams<TableProduct>) => (
        <EditablePriceCellRenderer
          data={params.data!}
          onUpdate={(id, updates) => {
            updateTempProduct(id, updates);
            // Check if product should be saved
            const updatedProduct = tempProducts.find(p => p.id === id);
            if (updatedProduct) {
              const finalProduct = { ...updatedProduct, ...updates };
              if (finalProduct.name && finalProduct.priceCents !== null && finalProduct.categoryId) {
                saveTempProduct(finalProduct);
              }
            }
          }}
        />
      ),
      sortable: true,
      filter: 'agNumberColumnFilter'
    },
    {
      headerName: 'Kategorie',
      field: 'categoryId',
      width: 200,
      cellRenderer: (params: ICellRendererParams<TableProduct>) => (
        <EditableCategoryCellRenderer
          data={params.data!}
          productCategories={categories}
          onUpdate={(id, updates) => {
            updateTempProduct(id, updates);
            // Check if product should be saved
            const updatedProduct = tempProducts.find(p => p.id === id);
            if (updatedProduct) {
              const finalProduct = { ...updatedProduct, ...updates };
              if (finalProduct.name && finalProduct.priceCents !== null && finalProduct.categoryId) {
                saveTempProduct(finalProduct);
              }
            }
          }}
        />
      ),
      sortable: true,
      filter: true
    },
    {
      headerName: 'Aktionen',
      width: 150,
      cellRenderer: (params: ICellRendererParams<TableProduct>) => (
        <ActionCellRenderer
          data={params.data!}
          onDelete={({id}) => deleteProduct(id)}
          onDeleteTemp={deleteTempProduct}
        />
      ),
      sortable: false,
      filter: false
    }
  ];

  const startContent = (
        <>
            <Button
              icon="pi pi-plus"
              className="mr-2"
              onClick={addTempProduct}
              label="Neues Produkt"
            />
        </>
    );

    const endContent = (
        <>
        </>
    );

  return (
    <Card>
      <Toolbar start={startContent} end={endContent} />
      <div className="ag-theme-alpine" style={{ height: 400, width: '100%' }}>
        <AgGridReact
          rowData={allProducts}
          columnDefs={columnDefs}
          defaultColDef={{
            resizable: true,
            sortable: true,
            filter: true
          }}
          pagination={true}
          paginationPageSize={10}
          domLayout="autoHeight"
          getRowId={(params) => params.data.id}
        />
      </div>
    </Card>
  );
}

export default AdminProductsPage;
