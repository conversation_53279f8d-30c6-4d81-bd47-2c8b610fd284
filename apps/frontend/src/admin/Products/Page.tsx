import { useState, useEffect } from "react";
import { useProductCategories } from "@/data/useProductCategories";
import { useProducts } from "@/data/useProducts";
import { Card } from "primereact/card";
import { Button } from "primereact/button";
import { Toolbar } from "primereact/toolbar";
import type { ColDef, ICellRendererParams } from 'ag-grid-community';
import { AgGridReact } from "ag-grid-react";
import type { Product, ProductCategory } from "@kassierer/shared/model";
import { PriceCentUtils } from "@/utils/priceCents";
import { NavLink } from "react-router-dom";


// Action cell renderer component
const ActionCellRenderer = ({ data, onDelete }: { data: Product; onDelete: (product: Product) => void }) => {
  return (
    <div className="flex gap-2">
      <Button
        label="Löschen"
        icon="pi pi-trash"
        severity="danger"
        size="small"
        onClick={() => onDelete(data)}
      />
    </div>
  );
};

// Price cell renderer component
const PriceCellRenderer = ({ data }: { data: Product }) => {
  return (
    <span>{PriceCentUtils.toMoneyString(data.priceCents)}</span>
  );
};

// Category cell renderer component
const CategoryCellRenderer = ({ data, productCategories }: { data: Product; productCategories: ProductCategory[] }) => {
  const category = productCategories.find(cat => cat.id === data.categoryId);
  if (category) {
    return (
      <NavLink
        to={`/admin/product-categories/${category.id}`}
        className="text-primary no-underline hover:underline"
      >
        {category.name}
      </NavLink>
    );
  }
  return <span>-</span>;
};


function AdminProductsPage() {
  const products = useProducts((state) => state.products);
  const deleteProduct = useProducts((state) => state.deleteProduct);
  const createProduct = useProducts((state) => state.createProduct);
  const categories = useProductCategories((state) => state.categories);

  const columnDefs: ColDef<Product>[] = [
    {
      headerName: 'Name',
      field: 'name',
      flex: 1,
      sortable: true,
      filter: true
    },
    {
      headerName: 'Preis',
      field: 'priceCents',
      width: 120,
      cellRenderer: (params: ICellRendererParams<Product>) => (
        <PriceCellRenderer data={params.data!} />
      ),
      sortable: true,
      filter: 'agNumberColumnFilter'
    },
    {
      headerName: 'Kategorie',
      field: 'categoryId',
      width: 150,
      cellRenderer: (params: ICellRendererParams<Product>) => (
        <CategoryCellRenderer data={params.data!} productCategories={categories} />
      ),
      sortable: true,
      filter: true
    },
    {
      headerName: 'Aktionen',
      width: 150,
      cellRenderer: (params: ICellRendererParams<Product>) => (
        <ActionCellRenderer data={params.data!} onDelete={({id}) => deleteProduct(id)} />
      ),
      sortable: false,
      filter: false
    }
  ];

  const startContent = (
        <>
            <Button icon="pi pi-plus" className="mr-2" />
        </>
    );

    const endContent = (
        <>
        </>
    );

  return (
    <Card>
      <Toolbar start={startContent} end={endContent} />
      <div className="ag-theme-alpine" style={{ height: 400, width: '100%' }}>
        <AgGridReact
          rowData={products}
          columnDefs={columnDefs}
          defaultColDef={{
            resizable: true,
            sortable: true,
            filter: true
          }}
          pagination={true}
          paginationPageSize={10}
          domLayout="autoHeight"
        />
      </div>
    </Card>
  );
}

export default AdminProductsPage;
