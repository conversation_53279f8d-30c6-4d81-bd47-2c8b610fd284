/* Sakai Layout Styles */

/* Variables */
:root {
  --border-radius: 12px;
  --transition-duration: 0.2s;
}

/* Mixins as CSS classes */
.focused {
  outline: 0 none;
  outline-offset: 0;
  transition: box-shadow 0.2s;
  box-shadow: var(--focus-ring);
}

.focused-inset {
  outline: 0 none;
  outline-offset: 0;
  transition: box-shadow 0.2s;
  box-shadow: inset var(--focus-ring);
}

/* Main Layout */
* {
  box-sizing: border-box;
}

html {
  height: 100%;
}

body {
  font-family: var(--font-family);
  color: var(--text-color);
  background-color: var(--surface-ground);
  margin: 0;
  padding: 0;
  min-height: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  text-decoration: none;
  color: var(--primary-color);
}

.layout-wrapper {
  min-height: 100vh;
}

/* Topbar */
.layout-topbar {
  position: fixed;
  height: 5rem;
  z-index: 997;
  left: 0;
  top: 0;
  width: 100%;
  padding: 0 2rem;
  background-color: var(--surface-card);
  transition: left var(--transition-duration);
  display: flex;
  align-items: center;
  box-shadow: 0px 3px 5px rgba(0,0,0,.02), 0px 0px 2px rgba(0,0,0,.05), 0px 1px 4px rgba(0,0,0,.08);
}

.layout-topbar .layout-topbar-logo {
  display: flex;
  align-items: center;
  color: var(--surface-900);
  font-size: 1.5rem;
  font-weight: 500;
  width: 300px;
  border-radius: 12px;
}

.layout-topbar .layout-topbar-logo img {
  height: 2.5rem;
  margin-right: 0.5rem;
}

.layout-topbar .layout-topbar-logo:focus {
  outline: 0 none;
  outline-offset: 0;
  transition: box-shadow 0.2s;
  box-shadow: var(--focus-ring);
}

.layout-topbar .layout-topbar-button {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  color: var(--text-color-secondary);
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  cursor: pointer;
  transition: background-color var(--transition-duration);
}

.layout-topbar .layout-topbar-button:hover {
  color: var(--text-color);
  background-color: var(--surface-hover);
}

.layout-topbar .layout-topbar-button:focus {
  outline: 0 none;
  outline-offset: 0;
  transition: box-shadow 0.2s;
  box-shadow: var(--focus-ring);
}

.layout-topbar .layout-topbar-button i {
  font-size: 1.5rem;
}

.layout-topbar .layout-topbar-button span {
  font-size: 1rem;
  display: none;
}

.layout-topbar .layout-menu-button {
  margin-left: 2rem;
}

.layout-topbar .layout-topbar-menu-button {
  display: none;
}

.layout-topbar .layout-topbar-menu-button i {
  font-size: 1.25rem;
}

.layout-topbar .layout-topbar-menu {
  margin: 0 0 0 auto;
  padding: 0;
  list-style: none;
  display: flex;
}

.layout-topbar .layout-topbar-menu .layout-topbar-button {
  margin-left: 1rem;
}

/* Sidebar */
.layout-sidebar {
  position: fixed;
  width: 300px;
  height: calc(100vh - 9rem);
  z-index: 999;
  overflow-y: auto;
  user-select: none;
  top: 7rem;
  left: 2rem;
  transition: transform var(--transition-duration), left var(--transition-duration);
  background-color: var(--surface-overlay);
  border-radius: var(--border-radius);
  padding: 0.5rem 1.5rem;
  box-shadow: 0px 3px 5px rgba(0, 0, 0, .02), 0px 0px 2px rgba(0, 0, 0, .05), 0px 1px 4px rgba(0, 0, 0, .08);
}

/* Menu */
.layout-menu {
  margin: 0;
  padding: 0;
  list-style-type: none;
  user-select: none;
}

.layout-menu .layout-root-menuitem > .layout-menuitem-root-text {
  font-size: 0.857rem;
  text-transform: uppercase;
  font-weight: 700;
  color: var(--surface-900);
  margin: 0.75rem 0;
}

.layout-menu .layout-root-menuitem > a {
  display: none;
}

.layout-menu li.active-menuitem > a .layout-submenu-toggler {
  transform: rotate(-180deg);
}

.layout-menu li.active-menuitem > ul {
  max-height: auto;
}

.layout-menu ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.layout-menu ul a {
  display: flex;
  align-items: center;
  position: relative;
  outline: 0 none;
  color: var(--text-color);
  cursor: pointer;
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  transition: background-color var(--transition-duration), box-shadow var(--transition-duration);
}

.layout-menu ul a .layout-menuitem-icon {
  margin-right: 0.5rem;
}

.layout-menu ul a .layout-submenu-toggler {
  font-size: 75%;
  margin-left: auto;
  transition: transform var(--transition-duration);
}

.layout-menu ul a.rotated-icon .layout-menuitem-icon {
  transform: rotate(90deg);
}

.layout-menu ul a.active-route {
  font-weight: 700;
  color: var(--primary-color);
}

.layout-menu ul a:hover {
  background-color: var(--surface-hover);
}

.layout-menu ul a:focus {
  outline: 0 none;
  outline-offset: 0;
  transition: box-shadow 0.2s;
  box-shadow: inset var(--focus-ring);
}

.layout-menu ul ul {
  overflow: hidden;
  max-height: 0;
  border-radius: var(--border-radius);
}

.layout-menu ul ul li a {
  margin-left: 1rem;
}

.layout-menu ul ul li li a {
  margin-left: 2rem;
}

.layout-menu ul ul li li li a {
  margin-left: 2.5rem;
}

.layout-menu ul ul li li li li a {
  margin-left: 3rem;
}

.layout-menu ul ul li li li li li a {
  margin-left: 3.5rem;
}

.layout-menu ul ul li li li li li li a {
  margin-left: 4rem;
}

/* Content */
.layout-main-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  justify-content: space-between;
  padding: 7rem 2rem 2rem 4rem;
  transition: margin-left var(--transition-duration);
}

.layout-main {
  flex: 1 1 auto;
}

/* Footer */
.layout-footer {
  transition: margin-left var(--transition-duration);
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 1rem;
  border-top: 1px solid var(--surface-border);
}

/* Blocked scroll */
.blocked-scroll {
  overflow: hidden;
}

/* Animations */
@keyframes scalein {
  0% {
    opacity: 0;
    transform: scaleY(0.8);
    transition: all var(--transition-duration) ease-out;
  }
  100% {
    opacity: 1;
    transform: scaleY(1);
  }
}

@keyframes fadein {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Responsive Styles */
@media screen and (min-width: 1960px) {
  .layout-main {
    width: 1504px;
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

@media (min-width: 992px) {
  .layout-wrapper.layout-overlay .layout-main-container {
    margin-left: 0;
    padding-left: 2rem;
  }

  .layout-wrapper.layout-overlay .layout-sidebar {
    transform: translateX(-100%);
    left: 0;
    top: 0;
    height: 100vh;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .layout-wrapper.layout-overlay.layout-overlay-active .layout-sidebar {
    transform: translateX(0);
  }

  .layout-wrapper.layout-static .layout-main-container {
    margin-left: 300px;
  }

  .layout-wrapper.layout-static.layout-static-inactive .layout-sidebar {
    transform: translateX(-100%);
    left: 0;
  }

  .layout-wrapper.layout-static.layout-static-inactive .layout-main-container {
    margin-left: 0;
    padding-left: 2rem;
  }

  .layout-wrapper .layout-mask {
    display: none;
  }
}

@media (max-width: 991px) {
  .layout-topbar {
    justify-content: space-between;
  }

  .layout-topbar .layout-topbar-logo {
    width: auto;
    order: 2;
  }

  .layout-topbar .layout-menu-button {
    margin-left: 0;
    order: 1;
  }

  .layout-topbar .layout-topbar-menu-button {
    display: inline-flex;
    margin-left: 0;
    order: 3;
  }

  .layout-topbar .layout-topbar-menu {
    margin-left: 0;
    position: absolute;
    flex-direction: column;
    background-color: var(--surface-overlay);
    box-shadow: 0px 3px 5px rgba(0,0,0,.02), 0px 0px 2px rgba(0,0,0,.05), 0px 1px 4px rgba(0,0,0,.08);
    border-radius: 12px;
    padding: 1rem;
    right: 2rem;
    top: 5rem;
    min-width: 15rem;
    display: none;
    animation: scalein 0.15s linear;
  }

  .layout-topbar .layout-topbar-menu.layout-topbar-menu-mobile-active {
    display: block;
  }

  .layout-topbar .layout-topbar-menu .layout-topbar-button {
    margin-left: 0;
    display: flex;
    width: 100%;
    height: auto;
    justify-content: flex-start;
    border-radius: 12px;
    padding: 1rem;
  }

  .layout-topbar .layout-topbar-menu .layout-topbar-button i {
    font-size: 1rem;
    margin-right: 0.5rem;
  }

  .layout-topbar .layout-topbar-menu .layout-topbar-button span {
    font-weight: medium;
    display: block;
  }

  .layout-wrapper .layout-main-container {
    margin-left: 0;
    padding-left: 2rem;
  }

  .layout-wrapper .layout-sidebar {
    transform: translateX(-100%);
    left: 0;
    top: 0;
    height: 100vh;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .layout-wrapper .layout-mask {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 998;
    width: 100%;
    height: 100%;
    background-color: var(--maskbg);
  }

  .layout-wrapper.layout-mobile-active .layout-sidebar {
    transform: translateX(0);
  }

  .layout-wrapper.layout-mobile-active .layout-mask {
    display: block;
    animation: fadein var(--transition-duration);
  }
}
