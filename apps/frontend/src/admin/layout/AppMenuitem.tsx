import { useLocation } from 'react-router-dom';
import { NavLink } from 'react-router-dom';
import { Ripple } from 'primereact/ripple';
import { classNames } from 'primereact/utils';
import React, { useContext } from 'react';
import { MenuContext } from './context/menucontext';
import type { AppMenuItem } from './layout.model';

interface AppMenuitemProps {
    item: AppMenuItem;
    root?: boolean;
    index?: number;
}

const AppMenuitem = ({ item, root, index }: AppMenuitemProps) => {
    const { activeMenu, setActiveMenu } = useContext(MenuContext);
    const location = useLocation();

    const isActiveRoute = () => {
        return location.pathname === item.to;
    };

    const itemClick = (event: React.MouseEvent<HTMLAnchorElement>) => {
        // avoid processing disabled items
        if (item.disabled) {
            event.preventDefault();
            return;
        }

        // execute command
        if (item.command) {
            item.command({ originalEvent: event, item: item });
        }

        // toggle active state
        if (item.items) {
            setActiveMenu(activeMenu === item.to ? '' : item.to || '');
        } else {
            setActiveMenu('');
        }
    };

    const subMenu = item.items && item.items.length && (
        <ul>
            {item.items.map((child, i) => {
                return <AppMenuitem item={child} index={i} key={child.label} />;
            })}
        </ul>
    );

    return (
        <li className={classNames({ 'layout-root-menuitem': root, 'active-menuitem': activeMenu === item.to })}>
            {root && item.visible !== false && <div className="layout-menuitem-root-text">{item.label}</div>}
            {(!item.to || item.items) && item.visible !== false ? (
                <a
                    href={item.url}
                    onClick={(e) => itemClick(e)}
                    className={classNames(item.class, 'p-ripple')}
                    target={item.target}
                    tabIndex={0}
                >
                    <i className={classNames('layout-menuitem-icon', item.icon)}></i>
                    <span className="layout-menuitem-text">{item.label}</span>
                    {item.items && <i className="pi pi-fw pi-angle-down layout-submenu-toggler"></i>}
                    <Ripple />
                </a>
            ) : null}

            {item.to && !item.items && item.visible !== false ? (
                <NavLink to={item.to} className={classNames(item.class, 'p-ripple', { 'active-route': isActiveRoute() })}>
                    <i className={classNames('layout-menuitem-icon', item.icon)}></i>
                    <span className="layout-menuitem-text">{item.label}</span>
                    {item.items && <i className="pi pi-fw pi-angle-down layout-submenu-toggler"></i>}
                    <Ripple />
                </NavLink>
            ) : null}

            {subMenu}
        </li>
    );
};

export default AppMenuitem;
