import React, { useContext } from 'react';
import AppMenuitem from './AppMenuitem';
import { LayoutContext } from './context/layoutcontext';
import { MenuProvider } from './context/menucontext';
import type { AppMenuItem } from './layout.model';

const AppMenu = () => {
    const { layoutConfig } = useContext(LayoutContext);

    const model: AppMenuItem[] = [
        {
            label: 'Admin',
            items: [
                { label: 'Produkte', icon: 'pi pi-fw pi-shopping-cart', to: '/admin/products' },
                { label: 'Kategorien', icon: 'pi pi-fw pi-tags', to: '/admin/product-categories' },
                { label: 'Tische', icon: 'pi pi-fw pi-table', to: '/admin/tables' },
                { label: 'Bestellungen', icon: 'pi pi-fw pi-list', to: '/admin/orders' }
            ]
        },
        {
            label: 'Kassierer',
            items: [
                { label: 'Kassierer App', icon: 'pi pi-fw pi-calculator', to: '/app/kassierer' }
            ]
        }
    ];

    return (
        <MenuProvider>
            <ul className="layout-menu">
                {model.map((item, i) => {
                    return !item?.seperator ? <AppMenuitem item={item} root={true} index={i} key={item.label} /> : <li className="menu-separator"></li>;
                })}
            </ul>
        </MenuProvider>
    );
};

export default AppMenu;
