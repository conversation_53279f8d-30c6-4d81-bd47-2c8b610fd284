import { NavLink } from 'react-router-dom';
import { classNames } from 'primereact/utils';
import React, { forwardRef, useContext, useImperativeHandle, useRef } from 'react';
import type { AppTopbarRef } from './layout.model';
import { LayoutContext } from './context/layoutcontext';
import { useUserStore } from '@/stores/userStore';
import { useNavigate } from 'react-router-dom';

const AppTopbar = forwardRef<AppTopbarRef>((props, ref) => {
    const { layoutConfig, layoutState, onMenuToggle, showProfileSidebar } = useContext(LayoutContext);
    const { user, logout } = useUserStore();
    const navigate = useNavigate();
    const menubuttonRef = useRef(null);
    const topbarmenuRef = useRef(null);
    const topbarmenubuttonRef = useRef(null);

    useImperativeHandle(ref, () => ({
        menubutton: menubuttonRef.current,
        topbarmenu: topbarmenuRef.current,
        topbarmenubutton: topbarmenubuttonRef.current
    }));

    const logoutAndRedirect = () => {
        logout();
        navigate('/login');
    };

    return (
        <div className="layout-topbar">
            <NavLink to="/admin/products" className="layout-topbar-logo">
                <span>Kassierer Admin</span>
            </NavLink>

            <button ref={menubuttonRef} type="button" className="p-link layout-menu-button layout-topbar-button" onClick={onMenuToggle}>
                <i className="pi pi-bars" />
            </button>

            <button ref={topbarmenubuttonRef} type="button" className="p-link layout-topbar-menu-button layout-topbar-button" onClick={showProfileSidebar}>
                <i className="pi pi-ellipsis-v" />
            </button>

            <div ref={topbarmenuRef} className={classNames('layout-topbar-menu', { 'layout-topbar-menu-mobile-active': layoutState.profileSidebarVisible })}>
                <button type="button" className="p-link layout-topbar-button">
                    <i className="pi pi-user"></i>
                    <span>{user?.email}</span>
                </button>
                <button type="button" className="p-link layout-topbar-button" onClick={logoutAndRedirect}>
                    <i className="pi pi-sign-out"></i>
                    <span>Ausloggen</span>
                </button>
            </div>
        </div>
    );
});

AppTopbar.displayName = 'AppTopbar';

export default AppTopbar;
